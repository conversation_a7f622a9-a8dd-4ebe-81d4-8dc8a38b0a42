using Oracle.ManagedDataAccess.Client;

namespace OracleDBSchemaSyncTool.Models
{
    public class DatabaseConnection
    {
        public string Server { get; set; } = string.Empty;
        public int Port { get; set; } = 1521;
        public string ServiceName { get; set; } = string.Empty;
        public string Username { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
        public string ConnectionName { get; set; } = string.Empty;

        public string GetConnectionString()
        {
            return $"Data Source={Server}:{Port}/{ServiceName};User Id={Username};Password={Password};";
        }

        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                using var connection = new OracleConnection(GetConnectionString());
                await connection.OpenAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }
    }
}
