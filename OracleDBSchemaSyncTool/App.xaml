<Application x:Class="OracleDBSchemaSyncTool.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:OracleDBSchemaSyncTool"
             xmlns:syncfusion="http://schemas.syncfusion.com/wpf"
             StartupUri="MainWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Syncfusion Material Light Theme -->
                <ResourceDictionary Source="/Syncfusion.Themes.MaterialLight.WPF;component/MSControl/Button.xaml" />
                <ResourceDictionary Source="/Syncfusion.Themes.MaterialLight.WPF;component/MSControl/TextBox.xaml" />
                <ResourceDictionary Source="/Syncfusion.Themes.MaterialLight.WPF;component/MSControl/TabControl.xaml" />
                <ResourceDictionary Source="/Syncfusion.Themes.MaterialLight.WPF;component/MSControl/GroupBox.xaml" />
                <ResourceDictionary Source="/Syncfusion.Themes.MaterialLight.WPF;component/MSControl/CheckBox.xaml" />
                <ResourceDictionary Source="/Syncfusion.Themes.MaterialLight.WPF;component/MSControl/DataGrid.xaml" />
                <ResourceDictionary Source="/Syncfusion.Themes.MaterialLight.WPF;component/SfGrid.WPF/SfDataGrid.xaml" />
                <ResourceDictionary Source="/Syncfusion.Themes.MaterialLight.WPF;component/SfInput.WPF/SfTextInputLayout.xaml" />
                <ResourceDictionary Source="/Syncfusion.Themes.MaterialLight.WPF;component/SfProgressBar.WPF/SfCircularProgressBar.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- Material Design Color Palette -->
            <SolidColorBrush x:Key="PrimaryBrush" Color="#6200EA"/>
            <SolidColorBrush x:Key="PrimaryLightBrush" Color="#9C47FF"/>
            <SolidColorBrush x:Key="PrimaryDarkBrush" Color="#3700B3"/>
            <SolidColorBrush x:Key="SecondaryBrush" Color="#03DAC6"/>
            <SolidColorBrush x:Key="SurfaceBrush" Color="#FFFFFF"/>
            <SolidColorBrush x:Key="BackgroundBrush" Color="#FAFAFA"/>
            <SolidColorBrush x:Key="ErrorBrush" Color="#B00020"/>
            <SolidColorBrush x:Key="OnPrimaryBrush" Color="#FFFFFF"/>
            <SolidColorBrush x:Key="OnSurfaceBrush" Color="#000000"/>
            <SolidColorBrush x:Key="OnBackgroundBrush" Color="#000000"/>

            <!-- Material Design Typography -->
            <Style x:Key="MaterialHeadline6" TargetType="TextBlock">
                <Setter Property="FontFamily" Value="Roboto"/>
                <Setter Property="FontSize" Value="20"/>
                <Setter Property="FontWeight" Value="Medium"/>
                <Setter Property="LineHeight" Value="32"/>
            </Style>

            <Style x:Key="MaterialSubtitle1" TargetType="TextBlock">
                <Setter Property="FontFamily" Value="Roboto"/>
                <Setter Property="FontSize" Value="16"/>
                <Setter Property="FontWeight" Value="Normal"/>
                <Setter Property="LineHeight" Value="24"/>
            </Style>

            <Style x:Key="MaterialBody1" TargetType="TextBlock">
                <Setter Property="FontFamily" Value="Roboto"/>
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="FontWeight" Value="Normal"/>
                <Setter Property="LineHeight" Value="20"/>
            </Style>
        </ResourceDictionary>
    </Application.Resources>
</Application>
