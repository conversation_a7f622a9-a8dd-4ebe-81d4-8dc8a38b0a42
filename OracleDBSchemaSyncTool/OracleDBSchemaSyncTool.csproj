<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Oracle.ManagedDataAccess.Core" Version="3.21.120" />
    <PackageReference Include="Syncfusion.SfGrid.WPF" Version="30.1.41" />
    <PackageReference Include="Syncfusion.SfInput.WPF" Version="30.1.41" />
    <PackageReference Include="Syncfusion.SfProgressBar.WPF" Version="30.1.41" />
    <PackageReference Include="Syncfusion.SfSkinManager.WPF" Version="30.1.41" />
    <PackageReference Include="Syncfusion.Themes.MaterialLight.WPF" Version="30.1.41" />
  </ItemGroup>

</Project>
