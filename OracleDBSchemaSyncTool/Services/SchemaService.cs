using Oracle.ManagedDataAccess.Client;
using Oracle.ManagedDataAccess.Types;
using OracleDBSchemaSyncTool.Models;

namespace OracleDBSchemaSyncTool.Services
{
    public class SchemaService
    {
        public async Task<List<SchemaObject>> GetSchemaObjectsAsync(DatabaseConnection connection, string schema)
        {
            var objects = new List<SchemaObject>();
            
            using var conn = new OracleConnection(connection.GetConnectionString());
            await conn.OpenAsync();

            // Get tables
            var tables = await GetTablesAsync(conn, schema);
            objects.AddRange(tables);

            // Get procedures and functions
            var procedures = await GetProceduresAsync(conn, schema);
            objects.AddRange(procedures);

            // Get views
            var views = await GetViewsAsync(conn, schema);
            objects.AddRange(views);

            // Get sequences
            var sequences = await GetSequencesAsync(conn, schema);
            objects.AddRange(sequences);

            return objects;
        }

        private async Task<List<TableInfo>> GetTablesAsync(OracleConnection connection, string schema)
        {
            var tables = new List<TableInfo>();
            
            var sql = @"
                SELECT table_name 
                FROM all_tables 
                WHERE owner = :schema 
                ORDER BY table_name";

            using var cmd = new OracleCommand(sql, connection);
            cmd.Parameters.Add(new OracleParameter("schema", OracleDbType.Varchar2) { Value = schema });
            
            using var reader = await cmd.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                var tableName = reader.GetString(0); // table_name
                var table = new TableInfo
                {
                    Name = tableName,
                    Type = "TABLE",
                    Owner = schema,
                    Columns = await GetColumnsAsync(connection, schema, tableName),
                    Indexes = await GetIndexesAsync(connection, schema, tableName),
                    Constraints = await GetConstraintsAsync(connection, schema, tableName)
                };
                tables.Add(table);
            }

            return tables;
        }

        private async Task<List<ColumnInfo>> GetColumnsAsync(OracleConnection connection, string schema, string tableName)
        {
            var columns = new List<ColumnInfo>();

            var sql = @"
                SELECT column_name, data_type, nullable, data_default
                FROM all_tab_columns
                WHERE owner = :schema AND table_name = :tableName
                ORDER BY column_id";

            using var cmd = new OracleCommand(sql, connection);
            cmd.Parameters.Add(new OracleParameter("schema", OracleDbType.Varchar2) { Value = schema });
            cmd.Parameters.Add(new OracleParameter("tableName", OracleDbType.Varchar2) { Value = tableName });

            using var reader = await cmd.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                columns.Add(new ColumnInfo
                {
                    Name = reader.GetString(0), // column_name
                    DataType = reader.GetString(1), // data_type
                    IsNullable = reader.GetString(2) == "Y", // nullable
                    DefaultValue = reader.IsDBNull(3) ? null : reader.GetString(3)?.Trim() // data_default
                });
            }

            return columns;
        }



        private async Task<List<IndexInfo>> GetIndexesAsync(OracleConnection connection, string schema, string tableName)
        {
            var indexes = new List<IndexInfo>();
            
            var sql = @"
                SELECT i.index_name, i.index_type, i.uniqueness,
                       LISTAGG(ic.column_name, ', ') WITHIN GROUP (ORDER BY ic.column_position) as columns
                FROM all_indexes i
                LEFT JOIN all_ind_columns ic ON i.index_name = ic.index_name AND i.owner = ic.index_owner
                WHERE i.owner = :schema AND i.table_name = :tableName
                  AND i.index_name NOT IN (
                      SELECT constraint_name FROM all_constraints 
                      WHERE owner = :schema AND table_name = :tableName 
                      AND constraint_type IN ('P', 'U')
                  )
                GROUP BY i.index_name, i.index_type, i.uniqueness
                ORDER BY i.index_name";

            using var cmd = new OracleCommand(sql, connection);
            cmd.Parameters.Add(new OracleParameter("schema", OracleDbType.Varchar2) { Value = schema });
            cmd.Parameters.Add(new OracleParameter("tableName", OracleDbType.Varchar2) { Value = tableName });
            
            using var reader = await cmd.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                var columnsStr = reader.IsDBNull(3) ? "" : reader.GetString(3); // columns
                indexes.Add(new IndexInfo
                {
                    Name = reader.GetString(0), // index_name
                    Type = reader.GetString(1), // index_type
                    IsUnique = reader.GetString(2) == "UNIQUE", // uniqueness
                    Columns = columnsStr.Split(new[] { ", " }, StringSplitOptions.RemoveEmptyEntries).ToList()
                });
            }

            return indexes;
        }

        private async Task<List<ConstraintInfo>> GetConstraintsAsync(OracleConnection connection, string schema, string tableName)
        {
            var constraints = new List<ConstraintInfo>();

            var sql = @"
                SELECT c.constraint_name, c.constraint_type, c.r_constraint_name,
                       LISTAGG(cc.column_name, ', ') WITHIN GROUP (ORDER BY cc.position) as columns
                FROM all_constraints c
                LEFT JOIN all_cons_columns cc ON c.constraint_name = cc.constraint_name AND c.owner = cc.owner
                WHERE c.owner = :schema AND c.table_name = :tableName
                GROUP BY c.constraint_name, c.constraint_type, c.r_constraint_name
                ORDER BY c.constraint_name";

            using var cmd = new OracleCommand(sql, connection);
            cmd.Parameters.Add(new OracleParameter("schema", OracleDbType.Varchar2) { Value = schema });
            cmd.Parameters.Add(new OracleParameter("tableName", OracleDbType.Varchar2) { Value = tableName });

            using var reader = await cmd.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                var constraintType = reader.GetString(1); // constraint_type
                var columnsStr = reader.IsDBNull(3) ? "" : reader.GetString(3); // columns
                var rConstraintName = reader.IsDBNull(2) ? null : reader.GetString(2); // r_constraint_name

                var definition = GenerateConstraintDefinition(constraintType, columnsStr, null, rConstraintName);

                constraints.Add(new ConstraintInfo
                {
                    Name = reader.GetString(0), // constraint_name
                    Type = GetConstraintTypeName(constraintType),
                    Definition = definition
                });
            }

            return constraints;
        }

        private string GetConstraintTypeName(string constraintType)
        {
            return constraintType switch
            {
                "P" => "PRIMARY KEY",
                "U" => "UNIQUE",
                "R" => "FOREIGN KEY",
                "C" => "CHECK",
                _ => constraintType
            };
        }

        private string GenerateConstraintDefinition(string constraintType, string columns, string? searchCondition, string? rConstraintName)
        {
            return constraintType switch
            {
                "P" => $"PRIMARY KEY ({columns})",
                "U" => $"UNIQUE ({columns})",
                "R" => $"FOREIGN KEY ({columns}) REFERENCES {rConstraintName}",
                "C" => searchCondition ?? "CHECK constraint",
                _ => $"({columns})"
            };
        }

        private async Task<List<SchemaObject>> GetProceduresAsync(OracleConnection connection, string schema)
        {
            var procedures = new List<SchemaObject>();
            
            var sql = @"
                SELECT object_name, object_type
                FROM all_objects 
                WHERE owner = :schema 
                  AND object_type IN ('PROCEDURE', 'FUNCTION')
                  AND status = 'VALID'
                ORDER BY object_name";

            using var cmd = new OracleCommand(sql, connection);
            cmd.Parameters.Add(new OracleParameter("schema", OracleDbType.Varchar2) { Value = schema });
            
            using var reader = await cmd.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                procedures.Add(new SchemaObject
                {
                    Name = reader.GetString(0), // object_name
                    Type = reader.GetString(1), // object_type
                    Owner = schema
                });
            }

            return procedures;
        }

        private async Task<List<SchemaObject>> GetViewsAsync(OracleConnection connection, string schema)
        {
            var views = new List<SchemaObject>();
            
            var sql = @"
                SELECT view_name
                FROM all_views 
                WHERE owner = :schema 
                ORDER BY view_name";

            using var cmd = new OracleCommand(sql, connection);
            cmd.Parameters.Add(new OracleParameter("schema", OracleDbType.Varchar2) { Value = schema });
            
            using var reader = await cmd.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                views.Add(new SchemaObject
                {
                    Name = reader.GetString(0), // view_name
                    Type = "VIEW",
                    Owner = schema
                });
            }

            return views;
        }

        private async Task<List<SchemaObject>> GetSequencesAsync(OracleConnection connection, string schema)
        {
            var sequences = new List<SchemaObject>();
            
            var sql = @"
                SELECT sequence_name
                FROM all_sequences 
                WHERE sequence_owner = :schema 
                ORDER BY sequence_name";

            using var cmd = new OracleCommand(sql, connection);
            cmd.Parameters.Add(new OracleParameter("schema", OracleDbType.Varchar2) { Value = schema });
            
            using var reader = await cmd.ExecuteReaderAsync();
            while (await reader.ReadAsync())
            {
                sequences.Add(new SchemaObject
                {
                    Name = reader.GetString(0), // sequence_name
                    Type = "SEQUENCE",
                    Owner = schema
                });
            }

            return sequences;
        }
    }
}
