using System.Configuration;
using System.Data;
using System.Windows;
using Syncfusion.SfSkinManager;

namespace OracleDBSchemaSyncTool
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        public App()
        {
            // Initialize Syncfusion Material Light theme
            SfSkinManager.ApplyStylesOnApplication = true;
        }

        protected override void OnStartup(StartupEventArgs e)
        {
            // Set Material Light theme for all Syncfusion controls
            SfSkinManager.SetTheme(this, new Theme("MaterialLight"));
            base.OnStartup(e);
        }
    }
}
